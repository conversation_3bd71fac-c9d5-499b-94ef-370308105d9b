import LogoIcon from "@/assets/imgs/logo.svg";
import { links, navLinks } from "../links";
import { Link } from "@/i18n/navigation";
import { getTranslations } from "next-intl/server";
import { Button, buttonVariants } from "@/components/ui/button";
import { Sun } from "lucide-react";
import LanguageSelector from "./LanguageSelector";
import { cn } from "@/lib/utils";

const NavLinks = async ({ className }: { className?: string }) => {
  const t = await getTranslations("navigation");

  return (
    <nav className={cn("flex items-center justify-center", className)}>
      <ul className="flex gap-5">
        {Object.entries(navLinks).map(([key, link]) => (
          <li key={key}>
            <Link
              className={buttonVariants({ variant: "link" })}
              href={link.href}
            >
              {t(link.key)}
            </Link>
          </li>
        ))}
      </ul>
    </nav>
  );
};

type Props = {};

const Header = async (props: Props) => {
  const tNav = await getTranslations("navigation");
  return (
    <header className="container-x bg-neutral/80 sticky top-0 z-50 flex items-center justify-between gap-5 py-5 backdrop-blur-sm">
      <div className="flex-1">
        <Link href="/" className="w-fit">
          <LogoIcon className="h-10 w-auto" />
        </Link>
      </div>
      <NavLinks className="flex-2" />
      <span className="flex flex-1 items-center justify-end gap-5">
        <Button variant="ghost" size="icon">
          <Sun />
        </Button>
        <LanguageSelector />
        <Button variant="outline">{tNav(links.setWA.key)}</Button>
        <Button variant="outline">{tNav(links.signOn.key)}</Button>
      </span>
    </header>
  );
};

export default Header;
